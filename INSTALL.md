# Anyside Mini Browser 安装指南

## 快速安装步骤

### 1. 准备工作
确保您使用的是Chrome浏览器（版本88或更高）。

### 2. 下载扩展程序
- 下载项目文件到本地文件夹
- 解压到任意目录（如：`D:\Coding\Anyside`）

### 3. 安装扩展程序

#### 方法一：开发者模式安装（推荐）
1. 打开Chrome浏览器
2. 在地址栏输入：`chrome://extensions/`
3. 在页面右上角开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择项目文件夹（包含manifest.json的文件夹）
6. 点击"选择文件夹"完成安装

#### 方法二：拖拽安装
1. 打开Chrome扩展程序管理页面（`chrome://extensions/`）
2. 开启"开发者模式"
3. 将整个项目文件夹拖拽到扩展程序页面
4. 确认安装

### 4. 验证安装
安装成功后，您应该能看到：
- 扩展程序列表中出现"Anyside Mini Browser"
- 浏览器工具栏中出现Anyside图标
- 扩展程序状态显示为"已启用"

### 5. 首次使用
1. 点击工具栏中的Anyside图标
2. 选择"打开侧边栏"或直接点击图标
3. 侧边栏将在浏览器右侧打开
4. 开始使用迷你浏览器功能

## 权限说明

扩展程序需要以下权限：
- **侧边栏访问权限**: 用于在浏览器侧边栏中显示迷你浏览器
- **存储权限**: 用于保存收藏夹和用户设置
- **标签页权限**: 用于管理和获取浏览器标签页信息
- **网络访问权限**: 用于在迷你浏览器中加载网页内容

这些权限都是必需的，用于提供完整的功能体验。

## 故障排除

### 问题1：扩展程序无法加载
**解决方案**：
- 确保manifest.json文件存在且格式正确
- 检查文件夹路径是否正确
- 尝试重新加载扩展程序

### 问题2：侧边栏无法打开
**解决方案**：
- 确保Chrome版本支持Side Panel API（版本114+）
- 检查扩展程序是否已启用
- 尝试刷新当前页面后再次打开

### 问题3：某些网站无法在侧边栏中显示
**解决方案**：
- 这是正常现象，某些网站出于安全考虑禁止在iframe中加载
- 可以点击"在新标签页中打开"来访问这些网站

### 问题4：图标不显示
**解决方案**：
- 检查网络连接，确保可以访问Lucide Icons CDN
- 如果网络有限制，可以下载图标库到本地

## 更新扩展程序

当有新版本时：
1. 下载新版本文件
2. 替换原有文件
3. 在扩展程序管理页面点击"重新加载"按钮
4. 确认更新成功

## 卸载扩展程序

如需卸载：
1. 打开`chrome://extensions/`
2. 找到"Anyside Mini Browser"
3. 点击"移除"按钮
4. 确认卸载

## 技术支持

如果遇到其他问题：
1. 查看浏览器控制台是否有错误信息
2. 检查扩展程序的详细错误信息
3. 参考README.md中的常见问题部分
4. 提交Issue到项目仓库

---

**注意**: 这是一个开发版本的扩展程序，仅供学习和测试使用。在生产环境中使用前，请确保充分测试所有功能。
