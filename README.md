# Anyside Mini Browser

一个功能强大的Chrome浏览器扩展程序，提供侧边栏迷你浏览器功能，支持多标签页管理和网页收藏。

## 功能特性

### 🌟 核心功能
- **侧边栏迷你浏览器**: 使用Chrome Extension Side Panel API在浏览器侧边栏中打开迷你浏览器
- **多标签页管理**: 支持在侧边栏中同时打开多个网页，使用标签页系统进行管理
- **网页收藏**: 允许用户收藏网页，并在新标签页中展示收藏列表
- **快速搜索**: 支持在地址栏中直接搜索或输入网址

### 📱 标签页管理
- 当前激活标签页显示网页favicon图标、标题和关闭按钮
- 非激活标签页只显示favicon图标
- 支持在不同标签页之间快速切换
- 标签页拖拽排序（计划中）

### 🔖 收藏功能
- 一键收藏当前浏览的网页
- 在新标签页中展示收藏的网页列表
- 支持收藏夹管理和快速访问

### 🎨 界面设计
- 现代化的深色主题界面
- 使用Lucide Icons图标库提供精美图标
- 响应式设计，适配不同屏幕尺寸
- 流畅的动画效果和交互体验

## 安装方法

### 开发者模式安装

1. **下载源码**
   ```bash
   git clone <repository-url>
   cd anyside-mini-browser
   ```

2. **打开Chrome扩展程序管理页面**
   - 在Chrome浏览器中输入 `chrome://extensions/`
   - 或者通过菜单：更多工具 → 扩展程序

3. **启用开发者模式**
   - 在扩展程序页面右上角打开"开发者模式"开关

4. **加载扩展程序**
   - 点击"加载已解压的扩展程序"
   - 选择项目文件夹
   - 扩展程序将自动加载并显示在扩展程序列表中

## 使用方法

### 基本操作

1. **打开侧边栏**
   - 点击浏览器工具栏中的Anyside图标
   - 或者右键点击图标选择"打开侧边栏"

2. **浏览网页**
   - 在地址栏中输入网址或搜索关键词
   - 按Enter键或点击搜索按钮开始浏览

3. **管理标签页**
   - 点击"+"按钮创建新标签页
   - 点击标签页切换到对应网页
   - 点击标签页上的"×"按钮关闭标签页

4. **收藏网页**
   - 在浏览网页时点击工具栏中的收藏按钮（书签图标）
   - 收藏的网页将显示在新标签页的收藏夹部分

### 快捷功能

- **后退/前进**: 使用导航按钮浏览历史记录
- **刷新**: 重新加载当前页面
- **主页**: 快速返回Google首页
- **快速链接**: 在新标签页中点击预设的快速链接

## 技术架构

### 文件结构
```
anyside-mini-browser/
├── manifest.json          # 扩展程序配置文件
├── background.js          # 后台服务工作者
├── popup.html            # 弹出窗口HTML
├── sidepanel.html        # 侧边栏HTML
├── content.js            # 内容脚本
├── styles/               # 样式文件
│   ├── popup.css
│   └── sidepanel.css
├── scripts/              # JavaScript文件
│   ├── popup.js
│   └── sidepanel.js
├── icons/                # 图标资源
│   ├── icon16.png
│   ├── icon48.png
│   └── icon128.png
└── README.md
```

### 技术栈
- **Chrome Extension Manifest V3**: 使用最新的扩展程序规范
- **Chrome Side Panel API**: 实现侧边栏功能
- **Lucide Icons**: 提供精美的图标
- **现代CSS**: 使用Flexbox和Grid布局
- **原生JavaScript**: 无依赖的纯JavaScript实现

### 权限说明
- `sidePanel`: 使用侧边栏API
- `storage`: 存储收藏夹和设置
- `activeTab`: 获取当前标签页信息
- `tabs`: 管理浏览器标签页
- `bookmarks`: 访问浏览器收藏夹
- `history`: 访问浏览历史记录

## 开发说明

### 本地开发
1. 修改源码后，在扩展程序管理页面点击"重新加载"按钮
2. 使用Chrome DevTools调试扩展程序
3. 查看控制台输出和错误信息

### 构建发布
1. 确保所有功能正常工作
2. 压缩项目文件夹为ZIP格式
3. 上传到Chrome Web Store（需要开发者账号）

## 常见问题

### Q: 为什么某些网站无法在侧边栏中正常显示？
A: 由于安全策略，某些网站（如银行网站）可能禁止在iframe中加载。这是正常的安全限制。

### Q: 如何清除收藏夹？
A: 目前需要逐个删除收藏项，批量清除功能将在后续版本中添加。

### Q: 扩展程序占用内存过多怎么办？
A: 关闭不需要的标签页可以释放内存。每个标签页都会占用一定的内存资源。

## 更新日志

### v1.0.0 (2024-01-XX)
- 初始版本发布
- 实现基本的侧边栏浏览功能
- 支持多标签页管理
- 添加收藏功能
- 集成Lucide Icons图标库

## 贡献指南

欢迎提交Issue和Pull Request来改进这个项目！

## 许可证

MIT License - 详见LICENSE文件

## 联系方式

如有问题或建议，请通过以下方式联系：
- GitHub Issues: [项目地址]
- Email: [联系邮箱]
