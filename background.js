// Chrome扩展程序后台服务工作者
chrome.runtime.onInstalled.addListener(() => {
  console.log('Anyside Mini Browser 扩展程序已安装');
  
  // 初始化存储
  chrome.storage.local.set({
    bookmarks: [],
    tabs: [],
    currentTabId: null,
    settings: {
      theme: 'dark',
      defaultHomePage: 'chrome://newtab/'
    }
  });
});

// 处理扩展程序图标点击事件
chrome.action.onClicked.addListener(async (tab) => {
  // 打开侧边栏
  await chrome.sidePanel.open({ tabId: tab.id });
});

// 监听标签页更新事件
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  if (changeInfo.status === 'complete' && tab.url) {
    // 通知侧边栏更新
    chrome.runtime.sendMessage({
      type: 'TAB_UPDATED',
      tabId: tabId,
      url: tab.url,
      title: tab.title,
      favIconUrl: tab.favIconUrl
    }).catch(() => {
      // 忽略错误，可能侧边栏未打开
    });
  }
});

// 监听标签页关闭事件
chrome.tabs.onRemoved.addListener((tabId, removeInfo) => {
  chrome.runtime.sendMessage({
    type: 'TAB_REMOVED',
    tabId: tabId
  }).catch(() => {
    // 忽略错误
  });
});

// 处理来自侧边栏的消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  switch (message.type) {
    case 'OPEN_URL':
      handleOpenUrl(message.url, message.newTab);
      break;
    case 'GET_BOOKMARKS':
      getBookmarks().then(sendResponse);
      return true;
    case 'ADD_BOOKMARK':
      addBookmark(message.bookmark).then(sendResponse);
      return true;
    case 'REMOVE_BOOKMARK':
      removeBookmark(message.url).then(sendResponse);
      return true;
    case 'GET_TABS':
      getTabs().then(sendResponse);
      return true;
  }
});

// 打开URL
async function handleOpenUrl(url, newTab = false) {
  try {
    if (newTab) {
      await chrome.tabs.create({ url: url });
    } else {
      const [activeTab] = await chrome.tabs.query({ active: true, currentWindow: true });
      await chrome.tabs.update(activeTab.id, { url: url });
    }
  } catch (error) {
    console.error('打开URL失败:', error);
  }
}

// 获取收藏夹
async function getBookmarks() {
  try {
    const result = await chrome.storage.local.get(['bookmarks']);
    return result.bookmarks || [];
  } catch (error) {
    console.error('获取收藏夹失败:', error);
    return [];
  }
}

// 添加收藏
async function addBookmark(bookmark) {
  try {
    const result = await chrome.storage.local.get(['bookmarks']);
    const bookmarks = result.bookmarks || [];
    
    // 检查是否已存在
    const exists = bookmarks.some(b => b.url === bookmark.url);
    if (!exists) {
      bookmarks.push({
        ...bookmark,
        id: Date.now().toString(),
        dateAdded: new Date().toISOString()
      });
      await chrome.storage.local.set({ bookmarks });
    }
    
    return bookmarks;
  } catch (error) {
    console.error('添加收藏失败:', error);
    return [];
  }
}

// 删除收藏
async function removeBookmark(url) {
  try {
    const result = await chrome.storage.local.get(['bookmarks']);
    const bookmarks = result.bookmarks || [];
    const filteredBookmarks = bookmarks.filter(b => b.url !== url);
    await chrome.storage.local.set({ bookmarks: filteredBookmarks });
    return filteredBookmarks;
  } catch (error) {
    console.error('删除收藏失败:', error);
    return [];
  }
}

// 获取标签页信息
async function getTabs() {
  try {
    const tabs = await chrome.tabs.query({ currentWindow: true });
    return tabs.map(tab => ({
      id: tab.id,
      url: tab.url,
      title: tab.title,
      favIconUrl: tab.favIconUrl,
      active: tab.active
    }));
  } catch (error) {
    console.error('获取标签页失败:', error);
    return [];
  }
}
