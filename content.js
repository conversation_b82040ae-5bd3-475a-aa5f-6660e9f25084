// 内容脚本 - 在网页中注入的脚本
(function() {
  'use strict';
  
  // 监听来自扩展程序的消息
  chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    switch (message.type) {
      case 'GET_PAGE_INFO':
        sendResponse(getPageInfo());
        break;
      case 'SCROLL_TO_TOP':
        window.scrollTo({ top: 0, behavior: 'smooth' });
        break;
      case 'SCROLL_TO_BOTTOM':
        window.scrollTo({ top: document.body.scrollHeight, behavior: 'smooth' });
        break;
      case 'FIND_TEXT':
        findTextOnPage(message.text);
        break;
    }
  });
  
  // 获取页面信息
  function getPageInfo() {
    const favicon = getFavicon();
    const title = document.title || 'Untitled';
    const url = window.location.href;
    const description = getMetaDescription();
    
    return {
      title,
      url,
      favicon,
      description,
      timestamp: new Date().toISOString()
    };
  }
  
  // 获取网站图标
  function getFavicon() {
    // 尝试多种方式获取favicon
    const selectors = [
      'link[rel="icon"]',
      'link[rel="shortcut icon"]',
      'link[rel="apple-touch-icon"]',
      'link[rel="apple-touch-icon-precomposed"]'
    ];
    
    for (const selector of selectors) {
      const link = document.querySelector(selector);
      if (link && link.href) {
        return link.href;
      }
    }
    
    // 默认favicon路径
    const defaultFavicon = `${window.location.protocol}//${window.location.host}/favicon.ico`;
    return defaultFavicon;
  }
  
  // 获取页面描述
  function getMetaDescription() {
    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
      return metaDescription.getAttribute('content');
    }
    
    const ogDescription = document.querySelector('meta[property="og:description"]');
    if (ogDescription) {
      return ogDescription.getAttribute('content');
    }
    
    return '';
  }
  
  // 在页面中查找文本
  function findTextOnPage(searchText) {
    if (!searchText) return;
    
    // 移除之前的高亮
    removeHighlights();
    
    // 创建搜索正则表达式
    const regex = new RegExp(searchText, 'gi');
    const walker = document.createTreeWalker(
      document.body,
      NodeFilter.SHOW_TEXT,
      null,
      false
    );
    
    const textNodes = [];
    let node;
    
    while (node = walker.nextNode()) {
      if (regex.test(node.textContent)) {
        textNodes.push(node);
      }
    }
    
    // 高亮匹配的文本
    textNodes.forEach(textNode => {
      const parent = textNode.parentNode;
      const highlightedHTML = textNode.textContent.replace(regex, '<mark class="anyside-highlight">$&</mark>');
      
      const wrapper = document.createElement('span');
      wrapper.innerHTML = highlightedHTML;
      
      parent.insertBefore(wrapper, textNode);
      parent.removeChild(textNode);
    });
    
    // 滚动到第一个匹配项
    const firstHighlight = document.querySelector('.anyside-highlight');
    if (firstHighlight) {
      firstHighlight.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  }
  
  // 移除高亮
  function removeHighlights() {
    const highlights = document.querySelectorAll('.anyside-highlight');
    highlights.forEach(highlight => {
      const parent = highlight.parentNode;
      parent.replaceChild(document.createTextNode(highlight.textContent), highlight);
      parent.normalize();
    });
  }
  
  // 添加高亮样式
  const style = document.createElement('style');
  style.textContent = `
    .anyside-highlight {
      background-color: #ffeb3b !important;
      color: #000 !important;
      padding: 2px 4px !important;
      border-radius: 2px !important;
      font-weight: bold !important;
    }
  `;
  document.head.appendChild(style);
  
  // 页面加载完成后发送页面信息
  if (document.readyState === 'complete') {
    sendPageInfo();
  } else {
    window.addEventListener('load', sendPageInfo);
  }
  
  function sendPageInfo() {
    const pageInfo = getPageInfo();
    chrome.runtime.sendMessage({
      type: 'PAGE_INFO_UPDATED',
      pageInfo: pageInfo
    }).catch(() => {
      // 忽略错误
    });
  }
  
})();
