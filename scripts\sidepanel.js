// 侧边栏主脚本
class MiniBrowser {
  constructor() {
    this.tabs = [];
    this.currentTabId = null;
    this.bookmarks = [];
    this.history = [];
    
    this.init();
  }
  
  async init() {
    this.initializeElements();
    this.bindEvents();
    await this.loadBookmarks();
    this.createNewTab();
    
    // 初始化Lucide图标
    if (typeof lucide !== 'undefined') {
      lucide.createIcons();
    }
  }
  
  initializeElements() {
    // 工具栏元素
    this.backBtn = document.getElementById('backBtn');
    this.forwardBtn = document.getElementById('forwardBtn');
    this.refreshBtn = document.getElementById('refreshBtn');
    this.homeBtn = document.getElementById('homeBtn');
    this.urlInput = document.getElementById('urlInput');
    this.goBtn = document.getElementById('goBtn');
    this.bookmarkBtn = document.getElementById('bookmarkBtn');
    this.newTabBtn = document.getElementById('newTabBtn');
    this.menuBtn = document.getElementById('menuBtn');
    
    // 标签页元素
    this.tabsContainer = document.getElementById('tabsContainer');
    this.addTabBtn = document.getElementById('addTabBtn');
    
    // 内容区域元素
    this.browserView = document.getElementById('browserView');
    this.newTabView = document.getElementById('newTabView');
    this.iframeContainer = document.getElementById('iframeContainer');
    this.loadingIndicator = document.getElementById('loadingIndicator');
    
    // 新标签页元素
    this.bookmarksGrid = document.getElementById('bookmarksGrid');
    
    // 右键菜单
    this.contextMenu = document.getElementById('contextMenu');
  }
  
  bindEvents() {
    // 导航按钮事件
    this.backBtn.addEventListener('click', () => this.goBack());
    this.forwardBtn.addEventListener('click', () => this.goForward());
    this.refreshBtn.addEventListener('click', () => this.refresh());
    this.homeBtn.addEventListener('click', () => this.goHome());
    
    // 地址栏事件
    this.urlInput.addEventListener('keypress', (e) => {
      if (e.key === 'Enter') {
        this.navigateToUrl(this.urlInput.value);
      }
    });
    this.goBtn.addEventListener('click', () => {
      this.navigateToUrl(this.urlInput.value);
    });
    
    // 工具栏按钮事件
    this.bookmarkBtn.addEventListener('click', () => this.toggleBookmark());
    this.newTabBtn.addEventListener('click', () => this.createNewTab());
    this.menuBtn.addEventListener('click', () => this.showMenu());
    
    // 标签页事件
    this.addTabBtn.addEventListener('click', () => this.createNewTab());
    
    // 右键菜单事件
    document.addEventListener('click', () => this.hideContextMenu());
    document.addEventListener('contextmenu', (e) => this.handleContextMenu(e));
    
    // 监听来自background的消息
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleMessage(message, sender, sendResponse);
    });
  }
  
  // 创建新标签页
  createNewTab(url = null) {
    const tabId = 'tab_' + Date.now();
    const tab = {
      id: tabId,
      title: '新标签页',
      url: url || 'about:blank',
      favicon: null,
      loading: false,
      canGoBack: false,
      canGoForward: false,
      history: [],
      historyIndex: -1
    };
    
    this.tabs.push(tab);
    this.renderTab(tab);
    this.switchToTab(tabId);
    
    if (url) {
      this.navigateToUrl(url);
    } else {
      this.showNewTabPage();
    }
    
    return tabId;
  }
  
  // 渲染标签页
  renderTab(tab) {
    const tabElement = document.createElement('div');
    tabElement.className = 'tab';
    tabElement.setAttribute('data-tab-id', tab.id);
    
    const favicon = tab.favicon || this.getDefaultFavicon();
    
    tabElement.innerHTML = `
      <img class="tab-favicon" src="${favicon}" alt="" onerror="this.src='${this.getDefaultFavicon()}'">
      <span class="tab-title">${tab.title}</span>
      <button class="tab-close" title="关闭标签页">
        <i data-lucide="x"></i>
      </button>
    `;
    
    // 标签页点击事件
    tabElement.addEventListener('click', (e) => {
      if (!e.target.closest('.tab-close')) {
        this.switchToTab(tab.id);
      }
    });
    
    // 关闭按钮事件
    const closeBtn = tabElement.querySelector('.tab-close');
    closeBtn.addEventListener('click', (e) => {
      e.stopPropagation();
      this.closeTab(tab.id);
    });
    
    this.tabsContainer.appendChild(tabElement);
    
    // 重新初始化图标
    if (typeof lucide !== 'undefined') {
      lucide.createIcons();
    }
  }
  
  // 切换到指定标签页
  switchToTab(tabId) {
    const tab = this.tabs.find(t => t.id === tabId);
    if (!tab) return;
    
    this.currentTabId = tabId;
    
    // 更新标签页样式
    document.querySelectorAll('.tab').forEach(tabEl => {
      tabEl.classList.remove('active');
    });
    document.querySelector(`[data-tab-id="${tabId}"]`).classList.add('active');
    
    // 更新地址栏
    this.urlInput.value = tab.url === 'about:blank' ? '' : tab.url;
    
    // 更新导航按钮状态
    this.updateNavigationButtons(tab);
    
    // 显示对应的内容
    if (tab.url === 'about:blank') {
      this.showNewTabPage();
    } else {
      this.showBrowserView(tab);
    }
    
    // 更新收藏按钮状态
    this.updateBookmarkButton(tab.url);
  }
  
  // 关闭标签页
  closeTab(tabId) {
    const tabIndex = this.tabs.findIndex(t => t.id === tabId);
    if (tabIndex === -1) return;
    
    // 移除标签页
    this.tabs.splice(tabIndex, 1);
    document.querySelector(`[data-tab-id="${tabId}"]`).remove();
    
    // 如果关闭的是当前标签页，切换到其他标签页
    if (this.currentTabId === tabId) {
      if (this.tabs.length > 0) {
        const newActiveIndex = Math.min(tabIndex, this.tabs.length - 1);
        this.switchToTab(this.tabs[newActiveIndex].id);
      } else {
        this.createNewTab();
      }
    }
  }
  
  // 导航到指定URL
  navigateToUrl(url) {
    if (!url.trim()) return;
    
    const currentTab = this.getCurrentTab();
    if (!currentTab) return;
    
    // 处理搜索查询
    if (!this.isValidUrl(url)) {
      url = `https://www.google.com/search?q=${encodeURIComponent(url)}`;
    } else if (!url.startsWith('http://') && !url.startsWith('https://')) {
      url = 'https://' + url;
    }
    
    currentTab.url = url;
    currentTab.loading = true;
    this.urlInput.value = url;
    
    this.showBrowserView(currentTab);
    this.updateTabTitle(currentTab.id, '加载中...');
    this.showLoading(true);
    
    // 更新历史记录
    this.addToHistory(currentTab, url);
  }
  
  // 显示浏览器视图
  showBrowserView(tab) {
    this.newTabView.style.display = 'none';
    this.browserView.style.display = 'block';
    
    // 创建或更新iframe
    let iframe = this.iframeContainer.querySelector(`[data-tab-id="${tab.id}"]`);
    if (!iframe) {
      iframe = document.createElement('iframe');
      iframe.className = 'browser-iframe';
      iframe.setAttribute('data-tab-id', tab.id);
      iframe.sandbox = 'allow-same-origin allow-scripts allow-forms allow-popups allow-top-navigation';
      this.iframeContainer.appendChild(iframe);
      
      // iframe加载事件
      iframe.addEventListener('load', () => {
        tab.loading = false;
        this.showLoading(false);
        this.updateTabFromIframe(tab, iframe);
      });
      
      iframe.addEventListener('error', () => {
        tab.loading = false;
        this.showLoading(false);
        this.updateTabTitle(tab.id, '加载失败');
      });
    }
    
    // 隐藏其他iframe
    this.iframeContainer.querySelectorAll('.browser-iframe').forEach(frame => {
      frame.style.display = frame === iframe ? 'block' : 'none';
    });
    
    if (iframe.src !== tab.url) {
      iframe.src = tab.url;
    }
  }
  
  // 显示新标签页
  showNewTabPage() {
    this.browserView.style.display = 'none';
    this.newTabView.style.display = 'block';
    this.showLoading(false);
  }
  
  // 获取当前标签页
  getCurrentTab() {
    return this.tabs.find(t => t.id === this.currentTabId);
  }
  
  // 更新标签页标题
  updateTabTitle(tabId, title) {
    const tab = this.tabs.find(t => t.id === tabId);
    if (tab) {
      tab.title = title;
      const tabElement = document.querySelector(`[data-tab-id="${tabId}"] .tab-title`);
      if (tabElement) {
        tabElement.textContent = title;
      }
    }
  }
  
  // 从iframe更新标签页信息
  updateTabFromIframe(tab, iframe) {
    try {
      const doc = iframe.contentDocument || iframe.contentWindow.document;
      const title = doc.title || '无标题';
      this.updateTabTitle(tab.id, title);
      
      // 尝试获取favicon
      const favicon = this.extractFavicon(doc) || this.getDefaultFavicon(tab.url);
      tab.favicon = favicon;
      
      const tabElement = document.querySelector(`[data-tab-id="${tab.id}"] .tab-favicon`);
      if (tabElement) {
        tabElement.src = favicon;
      }
    } catch (error) {
      // 跨域限制，无法访问iframe内容
      console.log('无法访问iframe内容（跨域限制）');
    }
  }
  
  // 提取favicon
  extractFavicon(doc) {
    const selectors = [
      'link[rel="icon"]',
      'link[rel="shortcut icon"]',
      'link[rel="apple-touch-icon"]'
    ];
    
    for (const selector of selectors) {
      const link = doc.querySelector(selector);
      if (link && link.href) {
        return link.href;
      }
    }
    
    return null;
  }
  
  // 获取默认favicon
  getDefaultFavicon(url = '') {
    if (url) {
      try {
        const domain = new URL(url).hostname;
        return `https://www.google.com/s2/favicons?domain=${domain}&sz=16`;
      } catch {
        // 忽略错误
      }
    }
    return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSJjdXJyZW50Q29sb3IiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIj48cGF0aCBkPSJNMTAgMTNhNSA1IDAgMCAwIDcuNTQtLjU0bC0zLTNhNSA1IDAgMCAwLTcuMDcgMS4wNyIvPjxwYXRoIGQ9Ik0xNC4wNyA2LjkzYTUgNSAwIDAgMCA3LjA3IDcuMDdsLTEuNzItMS43MiIvPjxwYXRoIGQ9Ik00LjkzIDQuOTNsMS43MiAxLjcyYTUgNSAwIDAgMCA3LjA3IDcuMDdsLTMtM2E1IDUgMCAwIDAtNy4wNy03LjA3eiIvPjwvc3ZnPg==';
  }
  
  // 验证URL格式
  isValidUrl(string) {
    try {
      new URL(string.startsWith('http') ? string : 'https://' + string);
      return true;
    } catch {
      return false;
    }
  }
  
  // 显示/隐藏加载指示器
  showLoading(show) {
    this.loadingIndicator.style.display = show ? 'block' : 'none';
  }

  // 导航功能
  goBack() {
    const tab = this.getCurrentTab();
    if (tab && tab.canGoBack && tab.historyIndex > 0) {
      tab.historyIndex--;
      const url = tab.history[tab.historyIndex];
      tab.url = url;
      this.urlInput.value = url;
      this.showBrowserView(tab);
      this.updateNavigationButtons(tab);
    }
  }

  goForward() {
    const tab = this.getCurrentTab();
    if (tab && tab.canGoForward && tab.historyIndex < tab.history.length - 1) {
      tab.historyIndex++;
      const url = tab.history[tab.historyIndex];
      tab.url = url;
      this.urlInput.value = url;
      this.showBrowserView(tab);
      this.updateNavigationButtons(tab);
    }
  }

  refresh() {
    const tab = this.getCurrentTab();
    if (tab && tab.url !== 'about:blank') {
      tab.loading = true;
      this.showLoading(true);
      const iframe = this.iframeContainer.querySelector(`[data-tab-id="${tab.id}"]`);
      if (iframe) {
        iframe.src = iframe.src;
      }
    }
  }

  goHome() {
    this.navigateToUrl('https://www.google.com');
  }

  // 更新导航按钮状态
  updateNavigationButtons(tab) {
    this.backBtn.disabled = !tab.canGoBack;
    this.forwardBtn.disabled = !tab.canGoForward;
  }

  // 添加到历史记录
  addToHistory(tab, url) {
    if (tab.historyIndex >= 0 && tab.historyIndex < tab.history.length - 1) {
      // 如果不在历史记录末尾，删除后面的记录
      tab.history = tab.history.slice(0, tab.historyIndex + 1);
    }

    tab.history.push(url);
    tab.historyIndex = tab.history.length - 1;

    tab.canGoBack = tab.historyIndex > 0;
    tab.canGoForward = false;

    this.updateNavigationButtons(tab);
  }

  // 收藏功能
  async toggleBookmark() {
    const tab = this.getCurrentTab();
    if (!tab || tab.url === 'about:blank') return;

    const isBookmarked = this.bookmarks.some(b => b.url === tab.url);

    if (isBookmarked) {
      await this.removeBookmark(tab.url);
    } else {
      await this.addBookmark({
        title: tab.title,
        url: tab.url,
        favicon: tab.favicon
      });
    }

    this.updateBookmarkButton(tab.url);
    this.renderBookmarks();
  }

  async addBookmark(bookmark) {
    try {
      const response = await chrome.runtime.sendMessage({
        type: 'ADD_BOOKMARK',
        bookmark: bookmark
      });
      this.bookmarks = response || [];
    } catch (error) {
      console.error('添加收藏失败:', error);
    }
  }

  async removeBookmark(url) {
    try {
      const response = await chrome.runtime.sendMessage({
        type: 'REMOVE_BOOKMARK',
        url: url
      });
      this.bookmarks = response || [];
    } catch (error) {
      console.error('删除收藏失败:', error);
    }
  }

  async loadBookmarks() {
    try {
      const response = await chrome.runtime.sendMessage({
        type: 'GET_BOOKMARKS'
      });
      this.bookmarks = response || [];
      this.renderBookmarks();
    } catch (error) {
      console.error('加载收藏失败:', error);
      this.bookmarks = [];
    }
  }

  updateBookmarkButton(url) {
    const isBookmarked = this.bookmarks.some(b => b.url === url);
    this.bookmarkBtn.style.color = isBookmarked ? '#4f46e5' : '#ccc';
    this.bookmarkBtn.title = isBookmarked ? '取消收藏' : '收藏此页面';
  }

  renderBookmarks() {
    if (!this.bookmarksGrid) return;

    if (this.bookmarks.length === 0) {
      this.bookmarksGrid.innerHTML = `
        <div class="empty-bookmarks">
          <i data-lucide="bookmark"></i>
          <p>暂无收藏的网页</p>
          <p>点击地址栏旁的收藏按钮来收藏网页</p>
        </div>
      `;
    } else {
      this.bookmarksGrid.innerHTML = this.bookmarks.map(bookmark => `
        <div class="bookmark-item" data-url="${bookmark.url}">
          <img class="bookmark-favicon" src="${bookmark.favicon || this.getDefaultFavicon(bookmark.url)}" alt="" onerror="this.src='${this.getDefaultFavicon()}'">
          <div class="bookmark-info">
            <div class="bookmark-title">${bookmark.title}</div>
            <div class="bookmark-url">${new URL(bookmark.url).hostname}</div>
          </div>
        </div>
      `).join('');

      // 绑定收藏项点击事件
      this.bookmarksGrid.querySelectorAll('.bookmark-item').forEach(item => {
        item.addEventListener('click', () => {
          const url = item.getAttribute('data-url');
          this.navigateToUrl(url);
        });
      });
    }

    // 重新初始化图标
    if (typeof lucide !== 'undefined') {
      lucide.createIcons();
    }
  }

  // 处理消息
  handleMessage(message, sender, sendResponse) {
    switch (message.type) {
      case 'SHOW_BOOKMARKS':
        this.showNewTabPage();
        break;
      case 'TAB_UPDATED':
        this.handleTabUpdated(message);
        break;
      case 'TAB_REMOVED':
        this.handleTabRemoved(message);
        break;
    }
  }

  handleTabUpdated(message) {
    // 处理浏览器标签页更新
    const tab = this.getCurrentTab();
    if (tab && tab.url === message.url) {
      this.updateTabTitle(tab.id, message.title);
      if (message.favIconUrl) {
        tab.favicon = message.favIconUrl;
        const tabElement = document.querySelector(`[data-tab-id="${tab.id}"] .tab-favicon`);
        if (tabElement) {
          tabElement.src = message.favIconUrl;
        }
      }
    }
  }

  handleTabRemoved(message) {
    // 处理浏览器标签页关闭
    // 这里可以添加相应的处理逻辑
  }

  // 右键菜单
  handleContextMenu(e) {
    e.preventDefault();
    this.showContextMenu(e.clientX, e.clientY);
  }

  showContextMenu(x, y) {
    this.contextMenu.style.display = 'block';
    this.contextMenu.style.left = x + 'px';
    this.contextMenu.style.top = y + 'px';

    // 确保菜单不超出视窗
    const rect = this.contextMenu.getBoundingClientRect();
    if (rect.right > window.innerWidth) {
      this.contextMenu.style.left = (x - rect.width) + 'px';
    }
    if (rect.bottom > window.innerHeight) {
      this.contextMenu.style.top = (y - rect.height) + 'px';
    }
  }

  hideContextMenu() {
    this.contextMenu.style.display = 'none';
  }

  // 显示菜单
  showMenu() {
    // 这里可以实现更多菜单功能
    console.log('显示菜单');
  }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
  window.miniBrowser = new MiniBrowser();
});
