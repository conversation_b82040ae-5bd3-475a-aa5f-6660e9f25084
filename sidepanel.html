<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Anyside Mini Browser</title>
  <link rel="stylesheet" href="styles/sidepanel.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/lucide@latest/dist/umd/lucide.js">
</head>
<body>
  <div class="sidepanel-container">
    <!-- 顶部工具栏 -->
    <div class="toolbar">
      <div class="nav-controls">
        <button id="backBtn" class="nav-btn" title="后退">
          <i data-lucide="arrow-left"></i>
        </button>
        <button id="forwardBtn" class="nav-btn" title="前进">
          <i data-lucide="arrow-right"></i>
        </button>
        <button id="refreshBtn" class="nav-btn" title="刷新">
          <i data-lucide="refresh-cw"></i>
        </button>
        <button id="homeBtn" class="nav-btn" title="主页">
          <i data-lucide="home"></i>
        </button>
      </div>
      
      <div class="address-bar">
        <input type="text" id="urlInput" placeholder="输入网址或搜索..." />
        <button id="goBtn" class="go-btn">
          <i data-lucide="search"></i>
        </button>
      </div>
      
      <div class="toolbar-actions">
        <button id="bookmarkBtn" class="action-btn" title="收藏">
          <i data-lucide="bookmark"></i>
        </button>
        <button id="newTabBtn" class="action-btn" title="新标签页">
          <i data-lucide="plus"></i>
        </button>
        <button id="menuBtn" class="action-btn" title="菜单">
          <i data-lucide="menu"></i>
        </button>
      </div>
    </div>
    
    <!-- 标签页栏 -->
    <div class="tabs-bar" id="tabsBar">
      <div class="tabs-container" id="tabsContainer">
        <!-- 标签页将动态生成 -->
      </div>
      <button id="addTabBtn" class="add-tab-btn" title="新建标签页">
        <i data-lucide="plus"></i>
      </button>
    </div>
    
    <!-- 主内容区域 -->
    <div class="main-content">
      <!-- 浏览器视图 -->
      <div class="browser-view" id="browserView">
        <div class="iframe-container" id="iframeContainer">
          <!-- iframe将动态创建 -->
        </div>
      </div>
      
      <!-- 新标签页视图 -->
      <div class="new-tab-view" id="newTabView" style="display: none;">
        <div class="new-tab-content">
          <div class="logo-section">
            <div class="logo">
              <i data-lucide="layers"></i>
              <h1>Anyside</h1>
            </div>
            <p>您的迷你浏览器</p>
          </div>
          
          <div class="quick-search">
            <div class="search-box">
              <i data-lucide="search"></i>
              <input type="text" id="quickSearchInput" placeholder="搜索或输入网址..." />
            </div>
          </div>
          
          <div class="bookmarks-section">
            <h2>
              <i data-lucide="bookmark"></i>
              收藏夹
            </h2>
            <div class="bookmarks-grid" id="bookmarksGrid">
              <!-- 收藏夹将动态生成 -->
            </div>
          </div>
          
          <div class="quick-links">
            <h2>
              <i data-lucide="zap"></i>
              快速访问
            </h2>
            <div class="links-grid">
              <a href="https://www.google.com" class="quick-link" data-url="https://www.google.com">
                <i data-lucide="search"></i>
                <span>Google</span>
              </a>
              <a href="https://github.com" class="quick-link" data-url="https://github.com">
                <i data-lucide="github"></i>
                <span>GitHub</span>
              </a>
              <a href="https://stackoverflow.com" class="quick-link" data-url="https://stackoverflow.com">
                <i data-lucide="help-circle"></i>
                <span>Stack Overflow</span>
              </a>
              <a href="https://developer.mozilla.org" class="quick-link" data-url="https://developer.mozilla.org">
                <i data-lucide="book"></i>
                <span>MDN</span>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 加载指示器 -->
    <div class="loading-indicator" id="loadingIndicator" style="display: none;">
      <div class="loading-spinner"></div>
    </div>
    
    <!-- 右键菜单 -->
    <div class="context-menu" id="contextMenu" style="display: none;">
      <div class="menu-item" id="openInNewTab">
        <i data-lucide="external-link"></i>
        在新标签页中打开
      </div>
      <div class="menu-item" id="copyLink">
        <i data-lucide="copy"></i>
        复制链接
      </div>
      <div class="menu-item" id="bookmarkPage">
        <i data-lucide="bookmark-plus"></i>
        收藏此页面
      </div>
    </div>
  </div>
  
  <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
  <script src="scripts/sidepanel.js"></script>
</body>
</html>
