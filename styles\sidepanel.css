/* 侧边栏样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: #1a1a1a;
  color: #ffffff;
  font-size: 14px;
  overflow: hidden;
}

.sidepanel-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
}

/* 工具栏样式 */
.toolbar {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #2a2a2a;
  border-bottom: 1px solid #333;
  flex-shrink: 0;
}

.nav-controls {
  display: flex;
  gap: 4px;
}

.nav-btn, .action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  background: transparent;
  color: #ccc;
  cursor: pointer;
  transition: all 0.2s ease;
}

.nav-btn:hover, .action-btn:hover {
  background: #404040;
  color: #fff;
}

.nav-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.nav-btn:disabled:hover {
  background: transparent;
  color: #ccc;
}

.address-bar {
  flex: 1;
  display: flex;
  align-items: center;
  background: #333;
  border-radius: 8px;
  padding: 0 12px;
  margin: 0 8px;
}

#urlInput {
  flex: 1;
  border: none;
  background: transparent;
  color: #fff;
  font-size: 14px;
  padding: 8px 0;
  outline: none;
}

#urlInput::placeholder {
  color: #888;
}

.go-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border: none;
  background: transparent;
  color: #888;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.go-btn:hover {
  background: #404040;
  color: #fff;
}

.toolbar-actions {
  display: flex;
  gap: 4px;
}

/* 标签页栏样式 */
.tabs-bar {
  display: flex;
  align-items: center;
  background: #2a2a2a;
  border-bottom: 1px solid #333;
  padding: 0 8px;
  flex-shrink: 0;
  overflow: hidden;
}

.tabs-container {
  display: flex;
  flex: 1;
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.tabs-container::-webkit-scrollbar {
  display: none;
}

.tab {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #333;
  border: 1px solid #444;
  border-bottom: none;
  border-radius: 8px 8px 0 0;
  margin-right: 2px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 120px;
  max-width: 200px;
  position: relative;
  white-space: nowrap;
}

.tab.active {
  background: #1a1a1a;
  border-color: #555;
  color: #fff;
}

.tab:not(.active) {
  opacity: 0.7;
}

.tab:hover:not(.active) {
  background: #404040;
}

.tab-favicon {
  width: 16px;
  height: 16px;
  border-radius: 2px;
  flex-shrink: 0;
}

.tab-title {
  flex: 1;
  font-size: 13px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.tab-close {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  border: none;
  background: transparent;
  color: #888;
  cursor: pointer;
  border-radius: 2px;
  opacity: 0;
  transition: all 0.2s ease;
}

.tab:hover .tab-close {
  opacity: 1;
}

.tab-close:hover {
  background: #555;
  color: #fff;
}

.add-tab-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border: none;
  background: transparent;
  color: #888;
  cursor: pointer;
  border-radius: 4px;
  margin-left: 4px;
  transition: all 0.2s ease;
}

.add-tab-btn:hover {
  background: #404040;
  color: #fff;
}

/* 主内容区域 */
.main-content {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.browser-view {
  width: 100%;
  height: 100%;
  position: relative;
}

.iframe-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.browser-iframe {
  width: 100%;
  height: 100%;
  border: none;
  background: #fff;
}

/* 新标签页样式 */
.new-tab-view {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  background: #1a1a1a;
}

.new-tab-content {
  padding: 40px 20px;
  max-width: 800px;
  margin: 0 auto;
}

.logo-section {
  text-align: center;
  margin-bottom: 40px;
}

.logo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  margin-bottom: 12px;
}

.logo i {
  width: 48px;
  height: 48px;
  color: #4f46e5;
}

.logo h1 {
  font-size: 32px;
  font-weight: 700;
  color: #fff;
}

.logo-section p {
  color: #888;
  font-size: 16px;
}

.quick-search {
  margin-bottom: 40px;
}

.search-box {
  display: flex;
  align-items: center;
  gap: 12px;
  background: #2a2a2a;
  border: 1px solid #333;
  border-radius: 12px;
  padding: 16px 20px;
  transition: all 0.2s ease;
}

.search-box:focus-within {
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.search-box i {
  color: #888;
  width: 20px;
  height: 20px;
}

#quickSearchInput {
  flex: 1;
  border: none;
  background: transparent;
  color: #fff;
  font-size: 16px;
  outline: none;
}

#quickSearchInput::placeholder {
  color: #888;
}

/* 收藏夹和快速链接 */
.bookmarks-section, .quick-links {
  margin-bottom: 40px;
}

.bookmarks-section h2, .quick-links h2 {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #fff;
}

.bookmarks-section h2 i, .quick-links h2 i {
  width: 20px;
  height: 20px;
  color: #4f46e5;
}

.bookmarks-grid, .links-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
}

.bookmark-item, .quick-link {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: #2a2a2a;
  border: 1px solid #333;
  border-radius: 8px;
  text-decoration: none;
  color: #fff;
  transition: all 0.2s ease;
  cursor: pointer;
}

.bookmark-item:hover, .quick-link:hover {
  background: #333;
  border-color: #4f46e5;
  transform: translateY(-1px);
}

.bookmark-favicon, .quick-link i {
  width: 20px;
  height: 20px;
  border-radius: 4px;
  flex-shrink: 0;
}

.quick-link i {
  color: #4f46e5;
}

.bookmark-info {
  flex: 1;
  min-width: 0;
}

.bookmark-title, .quick-link span {
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.bookmark-url {
  font-size: 12px;
  color: #888;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-top: 2px;
}

/* 加载指示器 */
.loading-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #333;
  border-top: 3px solid #4f46e5;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 右键菜单 */
.context-menu {
  position: fixed;
  background: #2a2a2a;
  border: 1px solid #333;
  border-radius: 8px;
  padding: 4px 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  min-width: 180px;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  cursor: pointer;
  transition: background 0.2s ease;
  font-size: 14px;
}

.menu-item:hover {
  background: #333;
}

.menu-item i {
  width: 16px;
  height: 16px;
  color: #888;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #1a1a1a;
}

::-webkit-scrollbar-thumb {
  background: #444;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* 空收藏夹样式 */
.empty-bookmarks {
  grid-column: 1 / -1;
  text-align: center;
  padding: 40px 20px;
  color: #666;
}

.empty-bookmarks i {
  width: 48px;
  height: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-bookmarks p {
  margin-bottom: 8px;
}

.empty-bookmarks p:first-of-type {
  font-size: 16px;
  font-weight: 500;
  color: #888;
}

.empty-bookmarks p:last-of-type {
  font-size: 14px;
  color: #666;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.tab {
  animation: fadeIn 0.2s ease-out;
}

.bookmark-item, .quick-link {
  animation: fadeIn 0.3s ease-out;
}

/* 焦点样式 */
.address-bar:focus-within {
  box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.3);
}

.search-box:focus-within {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.2);
}

/* 加载状态 */
.tab.loading .tab-title::after {
  content: '';
  display: inline-block;
  width: 12px;
  height: 12px;
  margin-left: 8px;
  border: 2px solid #333;
  border-top: 2px solid #4f46e5;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* 工具提示 */
.tooltip {
  position: relative;
}

.tooltip::after {
  content: attr(title);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: #333;
  color: #fff;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s ease;
  z-index: 1000;
}

.tooltip:hover::after {
  opacity: 1;
}

/* 响应式设计 */
@media (max-width: 400px) {
  .toolbar {
    padding: 6px 8px;
  }

  .nav-btn, .action-btn {
    width: 28px;
    height: 28px;
  }

  .address-bar {
    margin: 0 4px;
  }

  .new-tab-content {
    padding: 20px 16px;
  }

  .bookmarks-grid, .links-grid {
    grid-template-columns: 1fr;
  }

  .tab {
    min-width: 100px;
    max-width: 150px;
  }

  .tab-title {
    font-size: 12px;
  }
}
